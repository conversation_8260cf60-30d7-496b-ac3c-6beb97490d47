{"name": "tripos-backend", "version": "0.0.1", "description": "Tripos Backend API Service", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"setup": "npm install && npm run db:up && sleep 5 && npm run db:generate && npm run db:migrate:reset", "start": "nest start", "start:dev": "nest start --watch", "start:prod": "node dist/main", "build": "nest build", "db:up": "docker-compose up -d", "db:down": "docker-compose down", "db:logs": "docker-compose logs -f", "db:reset": "npm run db:down && npm run db:up && sleep 5 && npm run db:migrate:reset", "db:migrate": "npx prisma migrate dev", "db:migrate:reset": "npx prisma migrate reset --force", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "dev": "npm run db:up && sleep 5 && npm run db:generate && npm run start:dev", "clean": "rm -rf dist node_modules", "reset": "npm run clean && npm run setup", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:e2e": "jest --config ./test/jest-e2e.json", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\""}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.0", "@nestjs/platform-express": "^11.0.0", "@nestjs/serve-static": "^5.0.3", "@prisma/client": "^6.7.0", "@types/bcryptjs": "^2.4.6", "@types/file-type": "^10.6.0", "@types/multer": "^1.4.12", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "express": "^5.1.0", "file-type": "^20.5.0", "multer": "^1.4.5-lts.2", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^6.6.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}