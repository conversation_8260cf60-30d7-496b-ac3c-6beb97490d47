import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // Clear existing data
  await prisma.refreshToken.deleteMany();
  await prisma.carPhoto.deleteMany();
  await prisma.car.deleteMany();
  await prisma.trip.deleteMany();
  await prisma.order.deleteMany();
  await prisma.markedPlace.deleteMany();
  await prisma.stopPoint.deleteMany();
  await prisma.point.deleteMany();
  await prisma.user.deleteMany();

  // Hash passwords
  const password = await hash('password123', 12);

  // Create regular users (non-drivers)
  const regularUsers = await Promise.all([
    prisma.user.create({
      data: {
        phoneNumber: '+12345678901',
        firstName: '<PERSON>',
        lastName: 'Doe',
        password,
        isPhoneVerified: true,
      },
    }),
    prisma.user.create({
      data: {
        phoneNumber: '+12345678902',
        firstName: 'Jane',
        lastName: 'Smith',
        password,
        isPhoneVerified: true,
      },
    }),
  ]);

  // Create drivers with APPROVED status
  const approvedDrivers = await Promise.all([
    prisma.user.create({
      data: {
        phoneNumber: '+12345678903',
        firstName: 'Michael',
        lastName: 'Johnson',
        password,
        isPhoneVerified: true,
        driverStatus: 'APPROVED',
        IdCardFrontUrl: 'id_front.jpeg',
        IdCardBackUrl: 'id_back.jpeg',
        PersonalPhotoUrl: 'profile.jpeg',
      },
    }),
    prisma.user.create({
      data: {
        phoneNumber: '+12345678904',
        firstName: 'Sarah',
        lastName: 'Williams',
        password,
        isPhoneVerified: true,
        driverStatus: 'APPROVED',
        IdCardFrontUrl: 'id_front.jpeg',
        IdCardBackUrl: 'id_back.jpeg',
        PersonalPhotoUrl: 'profile.jpeg',
      },
    }),
  ]);

  // Create drivers with IN_REVIEW status
  const inReviewDrivers = await Promise.all([
    prisma.user.create({
      data: {
        phoneNumber: '+12345678905',
        firstName: 'David',
        lastName: 'Brown',
        password,
        isPhoneVerified: true,
        driverStatus: 'IN_REVIEW',
        IdCardFrontUrl: 'id_front.jpeg',
        IdCardBackUrl: 'id_back.jpeg',
        PersonalPhotoUrl: 'profile.jpeg',
      },
    }),
  ]);

  // Create cars for approved drivers
  const cars = await Promise.all([
    prisma.car.create({
      data: {
        userId: approvedDrivers[0].id,
        make: 'Toyota',
        model: 'Camry',
        year: 2020,
        licensePlate: 'ABC123',
      },
    }),
    prisma.car.create({
      data: {
        userId: approvedDrivers[1].id,
        make: 'Honda',
        model: 'Accord',
        year: 2019,
        licensePlate: 'XYZ789',
      },
    }),
  ]);

  // Create car photos
  await Promise.all([
    prisma.carPhoto.create({
      data: {
        carId: cars[0].id,
        photoUrl: 'car.jpeg',
      },
    }),
    prisma.carPhoto.create({
      data: {
        carId: cars[0].id,
        photoUrl: 'car.jpeg',
      },
    }),
    prisma.carPhoto.create({
      data: {
        carId: cars[1].id,
        photoUrl: 'car.jpeg',
      },
    }),
    prisma.carPhoto.create({
      data: {
        carId: cars[1].id,
        photoUrl: 'car.jpeg',
      },
    }),
  ]);

  // Create refresh tokens
  await Promise.all([
    prisma.refreshToken.create({
      data: {
        token: 'refresh_token_1',
        userId: regularUsers[0].id,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      },
    }),
    prisma.refreshToken.create({
      data: {
        token: 'refresh_token_2',
        userId: approvedDrivers[0].id,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      },
    }),
  ]);

  // Create stop points
  const stopPoints = await Promise.all([
    prisma.stopPoint.create({
      data: {
        name: 'Central Station',
        latitude: 40.7128,
        longitude: -74.006,
      },
    }),
    prisma.stopPoint.create({
      data: {
        name: 'City Mall',
        latitude: 40.7215,
        longitude: -74.0082,
      },
    }),
  ]);

  // Create marked places for users
  const markedPlaces = await Promise.all([
    prisma.markedPlace.create({
      data: {
        userId: regularUsers[0].id,
        name: 'Home',
        latitude: 40.7155,
        longitude: -74.0032,
      },
    }),
    prisma.markedPlace.create({
      data: {
        userId: regularUsers[1].id,
        name: 'Work',
        latitude: 40.7183,
        longitude: -74.0056,
      },
    }),
  ]);

  // Create points
  const points = await Promise.all([
    prisma.point.create({
      data: {
        latitude: 40.7135,
        longitude: -74.0045,
      },
    }),
    prisma.point.create({
      data: {
        latitude: 40.72,
        longitude: -74.007,
      },
    }),
  ]);

  // Create orders with different statuses
  const orders = await Promise.all([
    // Pending order with stop points
    prisma.order.create({
      data: {
        userId: regularUsers[0].id,
        status: 'PENDING',
        pickupPointId: stopPoints[0].id,
        dropoffPointId: stopPoints[1].id,
      },
    }),
    // Pending order with marked places
    prisma.order.create({
      data: {
        userId: regularUsers[1].id,
        status: 'PENDING',
        pickupPointId: markedPlaces[0].id,
        dropoffPointId: markedPlaces[1].id,
      },
    }),
    // Pending order with points
    prisma.order.create({
      data: {
        userId: regularUsers[0].id,
        status: 'PENDING',
        pickupPointId: points[0].id,
        dropoffPointId: points[1].id,
      },
    }),
    // Suggested order
    prisma.order.create({
      data: {
        userId: regularUsers[1].id,
        status: 'SUGGESTED_FOR_DRIVER',
        pickupPointId: stopPoints[0].id,
        dropoffPointId: stopPoints[1].id,
      },
    }),
  ]);

  console.log('Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
